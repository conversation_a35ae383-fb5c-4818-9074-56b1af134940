import jsonServer from "json-server";
const server = jsonServer.create();
const router = jsonServer.router("db.json");
const middlewares = jsonServer.defaults();

server.use(middlewares);
server.use(jsonServer.bodyParser);

// Custom routes for authentication
server.post("/register", (req, res) => {
  const { firstName, lastName, email, password } = req.body;

  // Check if user already exists
  const db = router.db;
  const existingUser = db.get("users").find({ email }).value();

  if (existingUser) {
    return res.status(400).json({ error: "User already exists" });
  }

  // Create new user
  const newUser = {
    id: Date.now(),
    firstName,
    lastName,
    email,
    password,
  };

  db.get("users").push(newUser).write();

  res.status(201).json({
    message: "User created successfully",
    user: {
      id: newUser.id,
      firstName: newUser.firstName,
      lastName: newUser.lastName,
      email: newUser.email,
    },
  });
});

server.post("/login", (req, res) => {
  const { email, password } = req.body;

  const db = router.db;
  const user = db.get("users").find({ email, password }).value();

  if (!user) {
    return res.status(401).json({ error: "Invalid credentials" });
  }

  res.json({
    user: {
      id: user.id,
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
    },
    accessToken: "fake-jwt-token-" + user.id,
  });
});

server.use(router);

const PORT = 5005;
server.listen(PORT, () => {
  console.log(`JSON Server is running on http://localhost:${PORT}`);
});
