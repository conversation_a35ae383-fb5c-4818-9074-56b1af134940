{"name": "ecommerce-front", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@hookform/resolvers": "^3.3.4", "@reduxjs/toolkit": "^2.0.1", "axios": "^1.6.5", "bootstrap": "^5.3.2", "lottie-react": "^2.4.0", "react": "^18.2.0", "react-bootstrap": "^2.10.0", "react-content-loader": "^7.0.0", "react-dom": "^18.2.0", "react-hook-form": "^7.51.2", "react-redux": "^9.1.0", "react-router-dom": "^6.21.3", "redux-persist": "^6.0.0", "vite-plugin-svgr": "^4.2.0", "vite-tsconfig-paths": "^4.3.1", "zod": "^3.22.4"}, "devDependencies": {"@types/node": "^20.11.25", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "typescript": "^5.2.2", "vite": "^5.0.8"}}