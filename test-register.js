import fetch from 'node-fetch';

const testData = {
  firstName: "Test",
  lastName: "User", 
  email: "<EMAIL>",
  password: "password123"
};

try {
  const response = await fetch('http://localhost:5005/register', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(testData)
  });
  
  const result = await response.text();
  console.log('Status:', response.status);
  console.log('Response:', result);
} catch (error) {
  console.error('Error:', error.message);
}
